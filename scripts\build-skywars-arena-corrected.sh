#!/bin/bash

# Professional SkyWars Arena Setup Script (Corrected)
# This script creates an advanced SkyWars arena with professional design
# Features: Multi-level center island, decorative elements, varied terrain

echo "=========================================="
echo "  BUILDING PROFESSIONAL SKYWARS ARENA    "
echo "=========================================="

# Set world properties
echo "Setting world properties..."
docker exec docker-minecraft-server-mc-1 rcon-cli --host localhost --port 25575 --password minecraft_rcon "time set day"
docker exec docker-minecraft-server-mc-1 rcon-cli --host localhost --port 25575 --password minecraft_rcon "weather clear"

# Clear the area first
echo "Clearing arena area..."
docker exec docker-minecraft-server-mc-1 rcon-cli --host localhost --port 25575 --password minecraft_rcon "fill -60 50 -60 60 120 60 air"

# Create professional center island (multi-level design)
echo "Creating professional center island..."

# Base level (stone foundation)
docker exec docker-minecraft-server-mc-1 rcon-cli --host localhost --port 25575 --password minecraft_rcon "fill -8 58 -8 8 60 8 stone"
docker exec docker-minecraft-server-mc-1 rcon-cli --host localhost --port 25575 --password minecraft_rcon "fill -6 61 -6 6 61 6 cobblestone"

# Second level (grass platform)
docker exec docker-minecraft-server-mc-1 rcon-cli --host localhost --port 25575 --password minecraft_rcon "fill -5 62 -5 5 62 5 grass_block"
docker exec docker-minecraft-server-mc-1 rcon-cli --host localhost --port 25575 --password minecraft_rcon "fill -4 63 -4 4 63 4 air"

# Third level (elevated center)
docker exec docker-minecraft-server-mc-1 rcon-cli --host localhost --port 25575 --password minecraft_rcon "fill -2 63 -2 2 63 2 stone_bricks"
docker exec docker-minecraft-server-mc-1 rcon-cli --host localhost --port 25575 --password minecraft_rcon "fill -1 64 -1 1 64 1 air"

# Decorative pillars around center
docker exec docker-minecraft-server-mc-1 rcon-cli --host localhost --port 25575 --password minecraft_rcon "fill 3 63 3 3 66 3 stone_brick_stairs[facing=west]"
docker exec docker-minecraft-server-mc-1 rcon-cli --host localhost --port 25575 --password minecraft_rcon "fill -3 63 3 -3 66 3 stone_brick_stairs[facing=east]"
docker exec docker-minecraft-server-mc-1 rcon-cli --host localhost --port 25575 --password minecraft_rcon "fill 3 63 -3 3 66 -3 stone_brick_stairs[facing=west]"
docker exec docker-minecraft-server-mc-1 rcon-cli --host localhost --port 25575 --password minecraft_rcon "fill -3 63 -3 -3 66 -3 stone_brick_stairs[facing=east]"

# Place premium center chests with better positioning
docker exec docker-minecraft-server-mc-1 rcon-cli --host localhost --port 25575 --password minecraft_rcon "setblock 0 64 0 chest[facing=north]"
docker exec docker-minecraft-server-mc-1 rcon-cli --host localhost --port 25575 --password minecraft_rcon "setblock 1 64 0 chest[facing=west]"
docker exec docker-minecraft-server-mc-1 rcon-cli --host localhost --port 25575 --password minecraft_rcon "setblock -1 64 0 chest[facing=east]"
docker exec docker-minecraft-server-mc-1 rcon-cli --host localhost --port 25575 --password minecraft_rcon "setblock 0 64 1 chest[facing=south]"

# Add enchanting table and anvil for center island
docker exec docker-minecraft-server-mc-1 rcon-cli --host localhost --port 25575 --password minecraft_rcon "setblock 2 64 2 enchanting_table"
docker exec docker-minecraft-server-mc-1 rcon-cli --host localhost --port 25575 --password minecraft_rcon "setblock -2 64 -2 anvil[facing=north]"

# Create professional player spawn islands (8 varied islands around center)
echo "Creating professional player spawn islands..."

# Island 1 (North) - Forest Theme
echo "Building Forest Island (North)..."
docker exec docker-minecraft-server-mc-1 rcon-cli --host localhost --port 25575 --password minecraft_rcon "fill -2 58 -27 2 60 -23 dirt"
docker exec docker-minecraft-server-mc-1 rcon-cli --host localhost --port 25575 --password minecraft_rcon "fill -1 61 -26 1 61 -24 grass_block"
docker exec docker-minecraft-server-mc-1 rcon-cli --host localhost --port 25575 --password minecraft_rcon "setblock 0 62 -25 oak_log"
docker exec docker-minecraft-server-mc-1 rcon-cli --host localhost --port 25575 --password minecraft_rcon "setblock 0 63 -25 oak_leaves"
docker exec docker-minecraft-server-mc-1 rcon-cli --host localhost --port 25575 --password minecraft_rcon "setblock 0 64 -25 oak_leaves"
docker exec docker-minecraft-server-mc-1 rcon-cli --host localhost --port 25575 --password minecraft_rcon "setblock 1 62 -25 chest[facing=west]"
docker exec docker-minecraft-server-mc-1 rcon-cli --host localhost --port 25575 --password minecraft_rcon "setblock -1 62 -24 crafting_table"

# Island 2 (Northeast) - Desert Theme
echo "Building Desert Island (Northeast)..."
docker exec docker-minecraft-server-mc-1 rcon-cli --host localhost --port 25575 --password minecraft_rcon "fill 16 58 -19 20 60 -15 sandstone"
docker exec docker-minecraft-server-mc-1 rcon-cli --host localhost --port 25575 --password minecraft_rcon "fill 17 61 -18 19 61 -16 sand"
docker exec docker-minecraft-server-mc-1 rcon-cli --host localhost --port 25575 --password minecraft_rcon "setblock 18 62 -17 cactus"
docker exec docker-minecraft-server-mc-1 rcon-cli --host localhost --port 25575 --password minecraft_rcon "setblock 18 63 -17 cactus"
docker exec docker-minecraft-server-mc-1 rcon-cli --host localhost --port 25575 --password minecraft_rcon "setblock 17 62 -17 chest[facing=east]"
docker exec docker-minecraft-server-mc-1 rcon-cli --host localhost --port 25575 --password minecraft_rcon "setblock 19 62 -16 furnace[facing=west]"

# Island 3 (East) - Mountain Theme
echo "Building Mountain Island (East)..."
docker exec docker-minecraft-server-mc-1 rcon-cli --host localhost --port 25575 --password minecraft_rcon "fill 22 58 -2 26 60 2 stone"
docker exec docker-minecraft-server-mc-1 rcon-cli --host localhost --port 25575 --password minecraft_rcon "fill 23 61 -1 25 61 1 cobblestone"
docker exec docker-minecraft-server-mc-1 rcon-cli --host localhost --port 25575 --password minecraft_rcon "setblock 24 62 0 stone_bricks"
docker exec docker-minecraft-server-mc-1 rcon-cli --host localhost --port 25575 --password minecraft_rcon "setblock 24 63 0 iron_block"
docker exec docker-minecraft-server-mc-1 rcon-cli --host localhost --port 25575 --password minecraft_rcon "setblock 23 62 0 chest[facing=east]"
docker exec docker-minecraft-server-mc-1 rcon-cli --host localhost --port 25575 --password minecraft_rcon "setblock 25 62 1 anvil[facing=north]"

# Island 4 (Southeast) - Nether Theme
echo "Building Nether Island (Southeast)..."
docker exec docker-minecraft-server-mc-1 rcon-cli --host localhost --port 25575 --password minecraft_rcon "fill 16 58 15 20 60 19 netherrack"
docker exec docker-minecraft-server-mc-1 rcon-cli --host localhost --port 25575 --password minecraft_rcon "fill 17 61 16 19 61 18 nether_bricks"
docker exec docker-minecraft-server-mc-1 rcon-cli --host localhost --port 25575 --password minecraft_rcon "setblock 18 62 17 fire"
docker exec docker-minecraft-server-mc-1 rcon-cli --host localhost --port 25575 --password minecraft_rcon "setblock 17 62 17 chest[facing=east]"
docker exec docker-minecraft-server-mc-1 rcon-cli --host localhost --port 25575 --password minecraft_rcon "setblock 19 62 18 brewing_stand"

# Island 5 (South) - Ocean Theme
echo "Building Ocean Island (South)..."
docker exec docker-minecraft-server-mc-1 rcon-cli --host localhost --port 25575 --password minecraft_rcon "fill -2 58 23 2 60 27 prismarine"
docker exec docker-minecraft-server-mc-1 rcon-cli --host localhost --port 25575 --password minecraft_rcon "fill -1 61 24 1 61 26 prismarine_bricks"
docker exec docker-minecraft-server-mc-1 rcon-cli --host localhost --port 25575 --password minecraft_rcon "setblock 0 62 25 sea_lantern"
docker exec docker-minecraft-server-mc-1 rcon-cli --host localhost --port 25575 --password minecraft_rcon "setblock 1 62 25 chest[facing=west]"
docker exec docker-minecraft-server-mc-1 rcon-cli --host localhost --port 25575 --password minecraft_rcon "setblock -1 62 24 sponge"

# Island 6 (Southwest) - Ice Theme
echo "Building Ice Island (Southwest)..."
docker exec docker-minecraft-server-mc-1 rcon-cli --host localhost --port 25575 --password minecraft_rcon "fill -20 58 15 -16 60 19 packed_ice"
docker exec docker-minecraft-server-mc-1 rcon-cli --host localhost --port 25575 --password minecraft_rcon "fill -19 61 16 -17 61 18 blue_ice"
docker exec docker-minecraft-server-mc-1 rcon-cli --host localhost --port 25575 --password minecraft_rcon "setblock -18 62 17 ice"
docker exec docker-minecraft-server-mc-1 rcon-cli --host localhost --port 25575 --password minecraft_rcon "setblock -17 62 17 chest[facing=east]"
docker exec docker-minecraft-server-mc-1 rcon-cli --host localhost --port 25575 --password minecraft_rcon "setblock -19 62 18 snow_block"

# Island 7 (West) - End Theme
echo "Building End Island (West)..."
docker exec docker-minecraft-server-mc-1 rcon-cli --host localhost --port 25575 --password minecraft_rcon "fill -26 58 -2 -22 60 2 end_stone"
docker exec docker-minecraft-server-mc-1 rcon-cli --host localhost --port 25575 --password minecraft_rcon "fill -25 61 -1 -23 61 1 end_stone_bricks"
docker exec docker-minecraft-server-mc-1 rcon-cli --host localhost --port 25575 --password minecraft_rcon "setblock -24 62 0 end_rod"
docker exec docker-minecraft-server-mc-1 rcon-cli --host localhost --port 25575 --password minecraft_rcon "setblock -23 62 0 chest[facing=east]"
docker exec docker-minecraft-server-mc-1 rcon-cli --host localhost --port 25575 --password minecraft_rcon "setblock -25 62 1 ender_chest"

# Island 8 (Northwest) - Jungle Theme
echo "Building Jungle Island (Northwest)..."
docker exec docker-minecraft-server-mc-1 rcon-cli --host localhost --port 25575 --password minecraft_rcon "fill -20 58 -19 -16 60 -15 dirt"
docker exec docker-minecraft-server-mc-1 rcon-cli --host localhost --port 25575 --password minecraft_rcon "fill -19 61 -18 -17 61 -16 grass_block"
docker exec docker-minecraft-server-mc-1 rcon-cli --host localhost --port 25575 --password minecraft_rcon "setblock -18 62 -17 jungle_log"
docker exec docker-minecraft-server-mc-1 rcon-cli --host localhost --port 25575 --password minecraft_rcon "setblock -18 63 -17 jungle_leaves"
docker exec docker-minecraft-server-mc-1 rcon-cli --host localhost --port 25575 --password minecraft_rcon "setblock -18 64 -17 jungle_leaves"
docker exec docker-minecraft-server-mc-1 rcon-cli --host localhost --port 25575 --password minecraft_rcon "setblock -17 62 -17 chest[facing=east]"
docker exec docker-minecraft-server-mc-1 rcon-cli --host localhost --port 25575 --password minecraft_rcon "setblock -19 62 -16 cocoa[age=2,facing=north]"

# Add floating mini-islands for extra loot
echo "Adding floating premium islands..."
docker exec docker-minecraft-server-mc-1 rcon-cli --host localhost --port 25575 --password minecraft_rcon "fill 10 75 10 12 75 12 gold_block"
docker exec docker-minecraft-server-mc-1 rcon-cli --host localhost --port 25575 --password minecraft_rcon "setblock 11 76 11 chest[facing=north]"

docker exec docker-minecraft-server-mc-1 rcon-cli --host localhost --port 25575 --password minecraft_rcon "fill -12 75 10 -10 75 12 diamond_block"
docker exec docker-minecraft-server-mc-1 rcon-cli --host localhost --port 25575 --password minecraft_rcon "setblock -11 76 11 chest[facing=north]"

docker exec docker-minecraft-server-mc-1 rcon-cli --host localhost --port 25575 --password minecraft_rcon "fill 10 75 -12 12 75 -10 emerald_block"
docker exec docker-minecraft-server-mc-1 rcon-cli --host localhost --port 25575 --password minecraft_rcon "setblock 11 76 -11 chest[facing=north]"

docker exec docker-minecraft-server-mc-1 rcon-cli --host localhost --port 25575 --password minecraft_rcon "fill -12 75 -12 -10 75 -10 iron_block"
docker exec docker-minecraft-server-mc-1 rcon-cli --host localhost --port 25575 --password minecraft_rcon "setblock -11 76 -11 chest[facing=north]"

# Create decorative sky structures
echo "Adding sky decorations..."
docker exec docker-minecraft-server-mc-1 rcon-cli --host localhost --port 25575 --password minecraft_rcon "setblock 0 85 0 beacon"
docker exec docker-minecraft-server-mc-1 rcon-cli --host localhost --port 25575 --password minecraft_rcon "fill -1 84 -1 1 84 1 iron_block"

# Create world border and death barrier
echo "Setting world border and barriers..."
docker exec docker-minecraft-server-mc-1 rcon-cli --host localhost --port 25575 --password minecraft_rcon "worldborder center 0 0"
docker exec docker-minecraft-server-mc-1 rcon-cli --host localhost --port 25575 --password minecraft_rcon "worldborder set 120"
docker exec docker-minecraft-server-mc-1 rcon-cli --host localhost --port 25575 --password minecraft_rcon "fill -60 45 -60 60 50 60 barrier"

echo "=========================================="
echo "  PROFESSIONAL SKYWARS ARENA COMPLETE!   "
echo "=========================================="
echo ""
echo "Arena Features Built:"
echo "✓ Multi-level center island with premium chests"
echo "✓ 8 themed spawn islands:"
echo "  - Forest Island (North) with oak trees"
echo "  - Desert Island (Northeast) with cactus"
echo "  - Mountain Island (East) with iron blocks"
echo "  - Nether Island (Southeast) with fire"
echo "  - Ocean Island (South) with sea lanterns"
echo "  - Ice Island (Southwest) with packed ice"
echo "  - End Island (West) with end rods"
echo "  - Jungle Island (Northwest) with jungle trees"
echo "✓ 4 floating premium islands (Gold, Diamond, Emerald, Iron)"
echo "✓ Sky beacon and decorative elements"
echo "✓ World border and death barriers"
echo ""
echo "The arena is ready for epic sky battles! 🏹⚔️"
echo "Player 'abusaker' can now explore the arena in creative mode."
